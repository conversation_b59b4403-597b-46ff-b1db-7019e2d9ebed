// 系统监控面板样式
.system-monitor-panel {
  .monitor-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
    margin-bottom: 24px;

    .monitor-card {
      background-color: #fff;
      border-radius: 8px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .card-title {
          font-size: 16px;
          font-weight: 500;
          color: #333;
        }

        .status-indicator {
          width: 8px;
          height: 8px;
          border-radius: 50%;

          &.online { background-color: #52c41a; }
          &.warning { background-color: #faad14; }
          &.offline { background-color: #ff4d4f; }
        }
      }

      .metric-display {
        .metric-value {
          font-size: 32px;
          font-weight: 600;
          color: #333;
          margin-bottom: 8px;
        }

        .metric-unit {
          font-size: 14px;
          color: #666;
        }

        .progress-bar {
          width: 100%;
          height: 8px;
          background-color: #f0f0f0;
          border-radius: 4px;
          overflow: hidden;
          margin-top: 12px;

          .progress-fill {
            height: 100%;
            transition: width 0.3s ease;

            &.normal { background-color: #52c41a; }
            &.warning { background-color: #faad14; }
            &.danger { background-color: #ff4d4f; }
          }
        }
      }
    }
  }

  .alerts-section {
    background-color: #fff;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 24px;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 16px;
    }

    .alert-list {
      .alert-item {
        display: flex;
        align-items: flex-start;
        padding: 12px;
        border-radius: 4px;
        margin-bottom: 8px;
        border-left: 4px solid;

        &.info {
          background-color: #e6f7ff;
          border-left-color: #1890ff;
        }

        &.warning {
          background-color: #fffbe6;
          border-left-color: #faad14;
        }

        &.error {
          background-color: #fff2f0;
          border-left-color: #ff4d4f;
        }

        &.critical {
          background-color: #fff0f6;
          border-left-color: #eb2f96;
        }

        .alert-icon {
          margin-right: 12px;
          font-size: 16px;
          margin-top: 2px;

          &.info { color: #1890ff; }
          &.warning { color: #faad14; }
          &.error { color: #ff4d4f; }
          &.critical { color: #eb2f96; }
        }

        .alert-content {
          flex: 1;

          .alert-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
          }

          .alert-description {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
          }

          .alert-time {
            font-size: 11px;
            color: #999;
          }
        }

        .alert-actions {
          display: flex;
          gap: 8px;

          .action-btn {
            padding: 2px 8px;
            border: 1px solid #d9d9d9;
            border-radius: 2px;
            background-color: #fff;
            color: #666;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s ease;

            &:hover {
              border-color: #1890ff;
              color: #1890ff;
            }
          }
        }
      }
    }
  }

  .performance-charts {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 24px;

    .chart-card {
      background-color: #fff;
      border-radius: 8px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

      .chart-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 16px;
      }

      .chart-container {
        height: 250px;
        position: relative;
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .system-monitor-panel {
    .monitor-grid {
      grid-template-columns: 1fr;
      gap: 16px;

      .monitor-card {
        padding: 16px;
      }
    }

    .alerts-section {
      padding: 16px;

      .alert-list {
        .alert-item {
          flex-direction: column;
          align-items: flex-start;

          .alert-actions {
            margin-top: 8px;
            align-self: flex-end;
          }
        }
      }
    }

    .performance-charts {
      grid-template-columns: 1fr;
      gap: 16px;

      .chart-card {
        padding: 16px;

        .chart-container {
          height: 200px;
        }
      }
    }
  }
}