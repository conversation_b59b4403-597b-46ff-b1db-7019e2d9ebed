<template>
  <view class="admin-content">
    <!-- 动态加载对应的管理模块 -->
    <component :is="currentComponent" />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useUIStore } from '@/stores/ui'
import DashboardPanel from './DashboardPanel.vue'

const uiStore = useUIStore()

// 当前选中的模块
const currentModule = ref('dashboard')

// 组件映射
const componentMap = {
  dashboard: DashboardPanel,
  // 其他模块组件将在后续任务中添加
}

// 当前组件
const currentComponent = computed(() => {
  return componentMap[currentModule.value] || DashboardPanel
})

onMounted(() => {
  // 默认显示仪表板
  currentModule.value = 'dashboard'
})

// 暴露切换模块的方法
const switchModule = (module: string) => {
  currentModule.value = module
}

defineExpose({
  switchModule
})
</script>

<style lang="scss" scoped>
@import '../styles/admin-content.scss';
</style>