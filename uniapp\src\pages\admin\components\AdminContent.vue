<template>
  <view class="admin-content">
    <!-- 动态加载对应的管理模块 -->
    <component :is="currentComponent" />
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useUIStore } from '@/stores/ui'
import { useDashboardStore } from '@/stores/dashboard'
import DashboardPanel from './DashboardPanel.vue'
import SystemMonitorPanel from '../modules/SystemMonitorPanel.vue'
import SystemFunctionPanel from '../modules/SystemFunctionPanel.vue'
import SystemToolsPanel from '../modules/SystemToolsPanel.vue'

const uiStore = useUIStore()
const dashboardStore = useDashboardStore()

// 当前选中的模块
const currentModule = ref('dashboard')

// 组件映射
const componentMap = {
  dashboard: DashboardPanel,
  'system-monitor': SystemMonitorPanel,
  'system-function': SystemFunctionPanel,
  'system-tools': SystemToolsPanel,
  // 子路由映射
  'system-function/user-management': SystemFunctionPanel,
  'system-function/role-management': SystemFunctionPanel,
  'system-function/partner-management': SystemFunctionPanel,
  'system-function/createChat-management': SystemFunctionPanel,
  'system-function/permission-management': SystemFunctionPanel,
  'system-tools/database-backup': SystemToolsPanel,
  'system-tools/log-viewer': SystemToolsPanel,
  'system-tools/cache-management': SystemToolsPanel,
  'system-tools/system-cleanup': SystemToolsPanel,
}

// 当前组件
const currentComponent = computed(() => {
  return componentMap[currentModule.value] || DashboardPanel
})

// 监听路由变化事件
const handleRouteChange = (path: string) => {
  currentModule.value = path
  console.log('AdminContent: 切换到模块', path)
}

onMounted(() => {
  // 默认显示仪表板
  currentModule.value = 'dashboard'

  // 监听路由切换事件
  uni.$on('admin-route-change', handleRouteChange)

  // 监听dashboard store的模块变化
  const unwatch = dashboardStore.$subscribe((mutation, state) => {
    if (mutation.type === 'direct' && mutation.events?.key === 'currentModule') {
      currentModule.value = state.currentModule || 'dashboard'
    }
  })

  onUnmounted(() => {
    unwatch()
  })
})

onUnmounted(() => {
  // 移除事件监听
  uni.$off('admin-route-change', handleRouteChange)
})

// 暴露切换模块的方法
const switchModule = (module: string) => {
  currentModule.value = module
  dashboardStore.setCurrentModule(module)
}

defineExpose({
  switchModule
})
</script>

<style lang="scss" scoped>
@import '../styles/admin-content.scss';
</style>