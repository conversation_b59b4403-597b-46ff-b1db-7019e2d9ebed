// 系统工具面板样式
.system-tools-panel {
  .tools-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;

    .tool-card {
      background-color: #fff;
      border-radius: 8px;
      padding: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      transition: transform 0.3s ease, box-shadow 0.3s ease;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      }

      .tool-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;

        .tool-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          background-color: #f0f0f0;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          color: #666;
          margin-right: 16px;

          &.backup { background-color: #e6f7ff; color: #1890ff; }
          &.logs { background-color: #f6ffed; color: #52c41a; }
          &.cache { background-color: #fff7e6; color: #faad14; }
          &.cleanup { background-color: #fff2f0; color: #ff4d4f; }
        }

        .tool-info {
          flex: 1;

          .tool-title {
            font-size: 16px;
            font-weight: 500;
            color: #333;
            margin-bottom: 4px;
          }

          .tool-description {
            font-size: 12px;
            color: #666;
          }
        }
      }

      .tool-content {
        margin-bottom: 16px;

        .tool-status {
          display: flex;
          align-items: center;
          margin-bottom: 12px;

          .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;

            &.ready { background-color: #52c41a; }
            &.running { background-color: #faad14; }
            &.error { background-color: #ff4d4f; }
          }

          .status-text {
            font-size: 12px;
            color: #666;
          }
        }

        .tool-metrics {
          .metric-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 4px 0;
            font-size: 12px;

            .metric-label {
              color: #666;
            }

            .metric-value {
              color: #333;
              font-weight: 500;
            }
          }
        }

        .progress-section {
          margin-top: 12px;

          .progress-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 4px;
          }

          .progress-bar {
            width: 100%;
            height: 6px;
            background-color: #f0f0f0;
            border-radius: 3px;
            overflow: hidden;

            .progress-fill {
              height: 100%;
              background-color: #1890ff;
              transition: width 0.3s ease;
            }
          }

          .progress-text {
            font-size: 11px;
            color: #999;
            margin-top: 4px;
            text-align: right;
          }
        }
      }

      .tool-actions {
        display: flex;
        gap: 8px;

        .action-btn {
          flex: 1;
          padding: 8px 16px;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          background-color: #fff;
          color: #333;
          font-size: 12px;
          cursor: pointer;
          transition: all 0.3s ease;
          text-align: center;

          &:hover {
            border-color: #1890ff;
            color: #1890ff;
          }

          &.primary {
            background-color: #1890ff;
            border-color: #1890ff;
            color: #fff;

            &:hover {
              background-color: #40a9ff;
            }
          }

          &.danger {
            border-color: #ff4d4f;
            color: #ff4d4f;

            &:hover {
              background-color: #ff4d4f;
              color: #fff;
            }
          }

          &:disabled {
            opacity: 0.5;
            cursor: not-allowed;

            &:hover {
              border-color: #d9d9d9;
              color: #333;
              background-color: #fff;
            }
          }
        }
      }
    }
  }

  .tool-modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;

    .modal-content {
      background-color: #fff;
      border-radius: 8px;
      padding: 24px;
      max-width: 500px;
      width: 90%;
      max-height: 80vh;
      overflow-y: auto;

      .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .modal-title {
          font-size: 16px;
          font-weight: 500;
          color: #333;
        }

        .close-btn {
          width: 24px;
          height: 24px;
          border: none;
          background: none;
          cursor: pointer;
          font-size: 16px;
          color: #666;

          &:hover {
            color: #333;
          }
        }
      }

      .modal-body {
        margin-bottom: 16px;

        .log-viewer {
          background-color: #f5f5f5;
          border-radius: 4px;
          padding: 12px;
          font-family: 'Courier New', monospace;
          font-size: 12px;
          max-height: 300px;
          overflow-y: auto;

          .log-line {
            margin-bottom: 2px;
            white-space: pre-wrap;

            &.error { color: #ff4d4f; }
            &.warning { color: #faad14; }
            &.info { color: #1890ff; }
            &.debug { color: #666; }
          }
        }

        .confirmation-text {
          font-size: 14px;
          color: #333;
          margin-bottom: 12px;
        }

        .warning-text {
          font-size: 12px;
          color: #ff4d4f;
          background-color: #fff2f0;
          padding: 8px;
          border-radius: 4px;
          border-left: 4px solid #ff4d4f;
        }
      }

      .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 8px;

        .footer-btn {
          padding: 8px 16px;
          border: 1px solid #d9d9d9;
          border-radius: 4px;
          background-color: #fff;
          color: #333;
          font-size: 12px;
          cursor: pointer;
          transition: all 0.3s ease;

          &:hover {
            border-color: #1890ff;
            color: #1890ff;
          }

          &.primary {
            background-color: #1890ff;
            border-color: #1890ff;
            color: #fff;

            &:hover {
              background-color: #40a9ff;
            }
          }

          &.danger {
            background-color: #ff4d4f;
            border-color: #ff4d4f;
            color: #fff;

            &:hover {
              background-color: #ff7875;
            }
          }
        }
      }
    }
  }
}

// 移动端适配
@media (max-width: 768px) {
  .system-tools-panel {
    .tools-grid {
      grid-template-columns: 1fr;
      gap: 16px;

      .tool-card {
        padding: 16px;

        .tool-header {
          .tool-icon {
            width: 40px;
            height: 40px;
            font-size: 20px;
            margin-right: 12px;
          }
        }

        .tool-actions {
          flex-direction: column;

          .action-btn {
            flex: none;
          }
        }
      }
    }

    .tool-modal {
      .modal-content {
        width: 95%;
        padding: 16px;

        .modal-body {
          .log-viewer {
            max-height: 200px;
          }
        }

        .modal-footer {
          flex-direction: column;

          .footer-btn {
            width: 100%;
          }
        }
      }
    }
  }
}